<?xml version="1.0" encoding="UTF-8"?>
<testsuites>
  <testsuite name="fastlane.lanes">
    
    
    
      
      <testcase classname="fastlane.lanes" name="00: default_platform" time="0.000126">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="01: Switch to ios build lane" time="5.3e-05">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="02: Switch to ios sh_on_root lane" time="4.4e-05">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="03: cd /Users/<USER>/scrumpass-exam-simulator &amp;&amp; flutter build ipa" time="143.078006">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="04: Switch to ios archive lane" time="0.000932">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="05: update_code_signing_settings" time="0.041889">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="06: Switch to ios increment_version lane" time="6.8e-05">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="07: firebase_app_distribution_get_latest_release" time="0.710219">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="08: increment_build_number" time="0.168549">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="09: update_project_team" time="0.005354">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="10: build_app" time="121.127189">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="11: firebase_app_distribution" time="9.566683">
        
      </testcase>
    
  </testsuite>
</testsuites>
