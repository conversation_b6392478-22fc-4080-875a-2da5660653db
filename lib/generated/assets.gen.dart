/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: directives_ordering,unnecessary_import,implicit_dynamic_list_literal,deprecated_member_use

import 'package:flutter/widgets.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:vector_graphics/vector_graphics.dart';

class $AssetsImagesGen {
  const $AssetsImagesGen();

  /// File path: assets/images/3d-fire 1.svg
  SvgGenImage get a3dFire1 => const SvgGenImage('assets/images/3d-fire 1.svg');

  /// File path: assets/images/Alt Arrow Right.svg
  SvgGenImage get altArrowRight =>
      const SvgGenImage('assets/images/Alt Arrow Right.svg');

  /// File path: assets/images/Alt Arrow Up.svg
  SvgGenImage get altArrowUp =>
      const SvgGenImage('assets/images/Alt Arrow Up.svg');

  /// File path: assets/images/Arrow Right Down.svg
  SvgGenImage get arrowRightDown =>
      const SvgGenImage('assets/images/Arrow Right Down.svg');

  /// File path: assets/images/Arrow Right Up.svg
  SvgGenImage get arrowRightUp =>
      const SvgGenImage('assets/images/Arrow Right Up.svg');

  /// File path: assets/images/Arrow Right.svg
  SvgGenImage get arrowRight =>
      const SvgGenImage('assets/images/Arrow Right.svg');

  /// File path: assets/images/BG.png
  AssetGenImage get bg => const AssetGenImage('assets/images/BG.png');

  /// File path: assets/images/Bell.svg
  SvgGenImage get bell => const SvgGenImage('assets/images/Bell.svg');

  /// File path: assets/images/Bolt.svg
  SvgGenImage get bolt => const SvgGenImage('assets/images/Bolt.svg');

  /// File path: assets/images/Book 1.png
  AssetGenImage get book1 => const AssetGenImage('assets/images/Book 1.png');

  /// File path: assets/images/Book.png
  AssetGenImage get bookPng => const AssetGenImage('assets/images/Book.png');

  /// File path: assets/images/Book.svg
  SvgGenImage get bookSvg => const SvgGenImage('assets/images/Book.svg');

  /// File path: assets/images/Book_review.svg
  SvgGenImage get bookReview =>
      const SvgGenImage('assets/images/Book_review.svg');

  /// File path: assets/images/Bookmark Square Minimalistic Exam Result.svg
  SvgGenImage get bookmarkSquareMinimalisticExamResult => const SvgGenImage(
      'assets/images/Bookmark Square Minimalistic Exam Result.svg');

  /// File path: assets/images/Bookmark Square Minimalistic.svg
  SvgGenImage get bookmarkSquareMinimalistic =>
      const SvgGenImage('assets/images/Bookmark Square Minimalistic.svg');

  /// File path: assets/images/Bookmark.svg
  SvgGenImage get bookmark => const SvgGenImage('assets/images/Bookmark.svg');

  /// File path: assets/images/Bookmarked.svg
  SvgGenImage get bookmarked =>
      const SvgGenImage('assets/images/Bookmarked.svg');

  /// File path: assets/images/Botom Cloud.png
  AssetGenImage get botomCloud =>
      const AssetGenImage('assets/images/Botom Cloud.png');

  /// File path: assets/images/Calendar.svg
  SvgGenImage get calendar => const SvgGenImage('assets/images/Calendar.svg');

  /// File path: assets/images/Chart 2.png
  AssetGenImage get chart2Png =>
      const AssetGenImage('assets/images/Chart 2.png');

  /// File path: assets/images/Chart 2.svg
  SvgGenImage get chart2Svg => const SvgGenImage('assets/images/Chart 2.svg');

  /// File path: assets/images/Chart 3.png
  AssetGenImage get chart3Png =>
      const AssetGenImage('assets/images/Chart 3.png');

  /// File path: assets/images/Chart 3.svg
  SvgGenImage get chart3Svg => const SvgGenImage('assets/images/Chart 3.svg');

  /// File path: assets/images/Checkbox_red.png
  AssetGenImage get checkboxRedPng =>
      const AssetGenImage('assets/images/Checkbox_red.png');

  /// File path: assets/images/Checkbox_red.svg
  SvgGenImage get checkboxRedSvg =>
      const SvgGenImage('assets/images/Checkbox_red.svg');

  /// File path: assets/images/Checkmark on White.svg
  SvgGenImage get checkmarkOnWhite =>
      const SvgGenImage('assets/images/Checkmark on White.svg');

  /// File path: assets/images/Clock Circle.svg
  SvgGenImage get clockCircle =>
      const SvgGenImage('assets/images/Clock Circle.svg');

  /// File path: assets/images/Close.svg
  SvgGenImage get close => const SvgGenImage('assets/images/Close.svg');

  /// File path: assets/images/Combo.svg
  SvgGenImage get combo => const SvgGenImage('assets/images/Combo.svg');

  /// File path: assets/images/Contact.svg
  SvgGenImage get contact => const SvgGenImage('assets/images/Contact.svg');

  /// File path: assets/images/Contact2.svg
  SvgGenImage get contact2 => const SvgGenImage('assets/images/Contact2.svg');

  /// File path: assets/images/Crown Line.svg
  SvgGenImage get crownLine =>
      const SvgGenImage('assets/images/Crown Line.svg');

  /// File path: assets/images/Danger.svg
  SvgGenImage get danger => const SvgGenImage('assets/images/Danger.svg');

  /// File path: assets/images/Danger_report.svg
  SvgGenImage get dangerReport =>
      const SvgGenImage('assets/images/Danger_report.svg');

  /// File path: assets/images/Dialog.svg
  SvgGenImage get dialog => const SvgGenImage('assets/images/Dialog.svg');

  /// File path: assets/images/Diploma Verified.svg
  SvgGenImage get diplomaVerified =>
      const SvgGenImage('assets/images/Diploma Verified.svg');

  /// File path: assets/images/Diploma.svg
  SvgGenImage get diploma => const SvgGenImage('assets/images/Diploma.svg');

  /// File path: assets/images/Diploma_grey.svg
  SvgGenImage get diplomaGrey =>
      const SvgGenImage('assets/images/Diploma_grey.svg');

  /// File path: assets/images/Document Add.svg
  SvgGenImage get documentAdd =>
      const SvgGenImage('assets/images/Document Add.svg');

  /// File path: assets/images/Document.svg
  SvgGenImage get document => const SvgGenImage('assets/images/Document.svg');

  /// File path: assets/images/Document_review_header.png
  AssetGenImage get documentReviewHeader =>
      const AssetGenImage('assets/images/Document_review_header.png');

  /// File path: assets/images/Documents Minimalistic.svg
  SvgGenImage get documentsMinimalistic =>
      const SvgGenImage('assets/images/Documents Minimalistic.svg');

  /// File path: assets/images/Fill Settings.svg
  SvgGenImage get fillSettings =>
      const SvgGenImage('assets/images/Fill Settings.svg');

  /// File path: assets/images/Finetuning.svg
  SvgGenImage get finetuning =>
      const SvgGenImage('assets/images/Finetuning.svg');

  /// File path: assets/images/Gift.svg
  SvgGenImage get gift => const SvgGenImage('assets/images/Gift.svg');

  /// File path: assets/images/Global.svg
  SvgGenImage get global => const SvgGenImage('assets/images/Global.svg');

  /// File path: assets/images/House_review_header.png
  AssetGenImage get houseReviewHeader =>
      const AssetGenImage('assets/images/House_review_header.png');

  /// File path: assets/images/Letter.svg
  SvgGenImage get letter => const SvgGenImage('assets/images/Letter.svg');

  /// File path: assets/images/Lightbulb Minimalistic.svg
  SvgGenImage get lightbulbMinimalistic =>
      const SvgGenImage('assets/images/Lightbulb Minimalistic.svg');

  /// File path: assets/images/Line Square Academic Cap.svg
  SvgGenImage get lineSquareAcademicCap =>
      const SvgGenImage('assets/images/Line Square Academic Cap.svg');

  /// File path: assets/images/Notebook Bookmark.svg
  SvgGenImage get notebookBookmark =>
      const SvgGenImage('assets/images/Notebook Bookmark.svg');

  /// File path: assets/images/Notebook.svg
  SvgGenImage get notebook => const SvgGenImage('assets/images/Notebook.svg');

  /// File path: assets/images/Notification Lines Remove.svg
  SvgGenImage get notificationLinesRemove =>
      const SvgGenImage('assets/images/Notification Lines Remove.svg');

  /// File path: assets/images/Palette.svg
  SvgGenImage get palette => const SvgGenImage('assets/images/Palette.svg');

  /// File path: assets/images/Point On Map.svg
  SvgGenImage get pointOnMap =>
      const SvgGenImage('assets/images/Point On Map.svg');

  /// File path: assets/images/Question Review.png
  AssetGenImage get questionReview =>
      const AssetGenImage('assets/images/Question Review.png');

  /// File path: assets/images/Restart Square.svg
  SvgGenImage get restartSquare =>
      const SvgGenImage('assets/images/Restart Square.svg');

  /// File path: assets/images/Review Header.png
  AssetGenImage get reviewHeader =>
      const AssetGenImage('assets/images/Review Header.png');

  /// File path: assets/images/Setting Users Group Two Rounded.svg
  SvgGenImage get settingUsersGroupTwoRounded =>
      const SvgGenImage('assets/images/Setting Users Group Two Rounded.svg');

  /// File path: assets/images/Setting_Book.svg
  SvgGenImage get settingBook =>
      const SvgGenImage('assets/images/Setting_Book.svg');

  /// File path: assets/images/Settings 1.png
  AssetGenImage get settings1 =>
      const AssetGenImage('assets/images/Settings 1.png');

  /// File path: assets/images/Settings.png
  AssetGenImage get settingsPng =>
      const AssetGenImage('assets/images/Settings.png');

  /// File path: assets/images/Settings.svg
  SvgGenImage get settingsSvg =>
      const SvgGenImage('assets/images/Settings.svg');

  /// File path: assets/images/Share.svg
  SvgGenImage get share => const SvgGenImage('assets/images/Share.svg');

  /// File path: assets/images/Shield Plus.svg
  SvgGenImage get shieldPlus =>
      const SvgGenImage('assets/images/Shield Plus.svg');

  /// File path: assets/images/Slider Vertical.svg
  SvgGenImage get sliderVertical =>
      const SvgGenImage('assets/images/Slider Vertical.svg');

  /// File path: assets/images/Smartphone 2.svg
  SvgGenImage get smartphone2 =>
      const SvgGenImage('assets/images/Smartphone 2.svg');

  /// File path: assets/images/Square Academic Cap 1.png
  AssetGenImage get squareAcademicCap1 =>
      const AssetGenImage('assets/images/Square Academic Cap 1.png');

  /// File path: assets/images/Square Academic Cap.png
  AssetGenImage get squareAcademicCapPng =>
      const AssetGenImage('assets/images/Square Academic Cap.png');

  /// File path: assets/images/Square Academic Cap.svg
  SvgGenImage get squareAcademicCapSvg =>
      const SvgGenImage('assets/images/Square Academic Cap.svg');

  /// File path: assets/images/Stars Minimalistic.svg
  SvgGenImage get starsMinimalistic =>
      const SvgGenImage('assets/images/Stars Minimalistic.svg');

  /// File path: assets/images/Test Review.png
  AssetGenImage get testReview =>
      const AssetGenImage('assets/images/Test Review.png');

  /// File path: assets/images/Text.svg
  SvgGenImage get text => const SvgGenImage('assets/images/Text.svg');

  /// File path: assets/images/Time.svg
  SvgGenImage get time => const SvgGenImage('assets/images/Time.svg');

  /// File path: assets/images/Time_Calendar.svg
  SvgGenImage get timeCalendar =>
      const SvgGenImage('assets/images/Time_Calendar.svg');

  /// File path: assets/images/Top Cloud.png
  AssetGenImage get topCloud =>
      const AssetGenImage('assets/images/Top Cloud.png');

  /// File path: assets/images/Up.svg
  SvgGenImage get up => const SvgGenImage('assets/images/Up.svg');

  /// File path: assets/images/User Rounded.svg
  SvgGenImage get userRounded =>
      const SvgGenImage('assets/images/User Rounded.svg');

  /// File path: assets/images/Users Group Two Rounded.svg
  SvgGenImage get usersGroupTwoRounded =>
      const SvgGenImage('assets/images/Users Group Two Rounded.svg');

  /// File path: assets/images/alert_popup_icon.png
  AssetGenImage get alertPopupIcon =>
      const AssetGenImage('assets/images/alert_popup_icon.png');

  /// File path: assets/images/analytic_bg_1.png
  AssetGenImage get analyticBg1 =>
      const AssetGenImage('assets/images/analytic_bg_1.png');

  /// File path: assets/images/analytic_bg_2.png
  AssetGenImage get analyticBg2 =>
      const AssetGenImage('assets/images/analytic_bg_2.png');

  /// File path: assets/images/analytic_bg_3.png
  AssetGenImage get analyticBg3 =>
      const AssetGenImage('assets/images/analytic_bg_3.png');

  /// File path: assets/images/analytic_unlock_pre.png
  AssetGenImage get analyticUnlockPre =>
      const AssetGenImage('assets/images/analytic_unlock_pre.png');

  /// File path: assets/images/arrow left.svg
  SvgGenImage get arrowLeft =>
      const SvgGenImage('assets/images/arrow left.svg');

  /// File path: assets/images/bg_tour.png
  AssetGenImage get bgTour => const AssetGenImage('assets/images/bg_tour.png');

  /// File path: assets/images/bigLightning.png
  AssetGenImage get bigLightning =>
      const AssetGenImage('assets/images/bigLightning.png');

  /// File path: assets/images/blue_flag.svg
  SvgGenImage get blueFlag => const SvgGenImage('assets/images/blue_flag.svg');

  /// File path: assets/images/board.png
  AssetGenImage get board => const AssetGenImage('assets/images/board.png');

  /// File path: assets/images/book_icon_high.png
  AssetGenImage get bookIconHigh =>
      const AssetGenImage('assets/images/book_icon_high.png');

  /// File path: assets/images/crown.png
  AssetGenImage get crown => const AssetGenImage('assets/images/crown.png');

  /// File path: assets/images/dart.png
  AssetGenImage get dart => const AssetGenImage('assets/images/dart.png');

  /// File path: assets/images/de_flag.png
  AssetGenImage get deFlag => const AssetGenImage('assets/images/de_flag.png');

  /// File path: assets/images/diamon_comment.png
  AssetGenImage get diamonComment =>
      const AssetGenImage('assets/images/diamon_comment.png');

  /// File path: assets/images/duration.svg
  SvgGenImage get duration => const SvgGenImage('assets/images/duration.svg');

  /// File path: assets/images/edu.png
  AssetGenImage get edu => const AssetGenImage('assets/images/edu.png');

  /// File path: assets/images/empty_icon.png
  AssetGenImage get emptyIcon =>
      const AssetGenImage('assets/images/empty_icon.png');

  /// File path: assets/images/en_flag.png
  AssetGenImage get enFlag => const AssetGenImage('assets/images/en_flag.png');

  /// File path: assets/images/exam_failed.png
  AssetGenImage get examFailed =>
      const AssetGenImage('assets/images/exam_failed.png');

  /// File path: assets/images/exam_passed.png
  AssetGenImage get examPassed =>
      const AssetGenImage('assets/images/exam_passed.png');

  /// File path: assets/images/facebook 1.svg
  SvgGenImage get facebook1 =>
      const SvgGenImage('assets/images/facebook 1.svg');

  /// File path: assets/images/forms_review.png
  AssetGenImage get formsReview =>
      const AssetGenImage('assets/images/forms_review.png');

  /// File path: assets/images/fr_flag.png
  AssetGenImage get frFlag => const AssetGenImage('assets/images/fr_flag.png');

  /// File path: assets/images/full_test.png
  AssetGenImage get fullTest =>
      const AssetGenImage('assets/images/full_test.png');

  /// File path: assets/images/function.svg
  SvgGenImage get function => const SvgGenImage('assets/images/function.svg');

  /// File path: assets/images/golden-star.svg
  SvgGenImage get goldenStar =>
      const SvgGenImage('assets/images/golden-star.svg');

  /// File path: assets/images/hand.png
  AssetGenImage get hand => const AssetGenImage('assets/images/hand.png');

  /// File path: assets/images/hi_flag.png
  AssetGenImage get hiFlag => const AssetGenImage('assets/images/hi_flag.png');

  /// File path: assets/images/home_light_1.png
  AssetGenImage get homeLight1 =>
      const AssetGenImage('assets/images/home_light_1.png');

  /// File path: assets/images/home_light_2.png
  AssetGenImage get homeLight2 =>
      const AssetGenImage('assets/images/home_light_2.png');

  /// File path: assets/images/ic_toast.svg
  SvgGenImage get icToast => const SvgGenImage('assets/images/ic_toast.svg');

  /// File path: assets/images/image 17.svg
  SvgGenImage get image17 => const SvgGenImage('assets/images/image 17.svg');

  /// File path: assets/images/info_popup_icon.png
  AssetGenImage get infoPopupIcon =>
      const AssetGenImage('assets/images/info_popup_icon.png');

  /// File path: assets/images/instruction_header.png
  AssetGenImage get instructionHeader =>
      const AssetGenImage('assets/images/instruction_header.png');

  /// File path: assets/images/king 1.svg
  SvgGenImage get king1 => const SvgGenImage('assets/images/king 1.svg');

  /// File path: assets/images/language 1.svg
  SvgGenImage get language1 =>
      const SvgGenImage('assets/images/language 1.svg');

  /// File path: assets/images/left_chart_image.png
  AssetGenImage get leftChartImage =>
      const AssetGenImage('assets/images/left_chart_image.png');

  /// File path: assets/images/light.svg
  SvgGenImage get light => const SvgGenImage('assets/images/light.svg');

  /// File path: assets/images/lightning.png
  AssetGenImage get lightning =>
      const AssetGenImage('assets/images/lightning.png');

  /// File path: assets/images/line.svg
  SvgGenImage get line => const SvgGenImage('assets/images/line.svg');

  /// File path: assets/images/luxury.svg
  SvgGenImage get luxury => const SvgGenImage('assets/images/luxury.svg');

  /// File path: assets/images/main_chart_image.png
  AssetGenImage get mainChartImage =>
      const AssetGenImage('assets/images/main_chart_image.png');

  /// File path: assets/images/messenger 1.svg
  SvgGenImage get messenger1 =>
      const SvgGenImage('assets/images/messenger 1.svg');

  /// File path: assets/images/mini_test.png
  AssetGenImage get miniTest =>
      const AssetGenImage('assets/images/mini_test.png');

  /// File path: assets/images/notes.png
  AssetGenImage get notes => const AssetGenImage('assets/images/notes.png');

  /// File path: assets/images/onboard_bg.png
  AssetGenImage get onboardBg =>
      const AssetGenImage('assets/images/onboard_bg.png');

  /// File path: assets/images/openBook.png
  AssetGenImage get openBook =>
      const AssetGenImage('assets/images/openBook.png');

  /// File path: assets/images/passing_score.svg
  SvgGenImage get passingScore =>
      const SvgGenImage('assets/images/passing_score.svg');

  /// File path: assets/images/passrate_full_bg.png
  AssetGenImage get passrateFullBg =>
      const AssetGenImage('assets/images/passrate_full_bg.png');

  /// File path: assets/images/passrate_small_bg.png
  AssetGenImage get passrateSmallBg =>
      const AssetGenImage('assets/images/passrate_small_bg.png');

  /// File path: assets/images/pen.png
  AssetGenImage get pen => const AssetGenImage('assets/images/pen.png');

  /// File path: assets/images/phone.png
  AssetGenImage get phone => const AssetGenImage('assets/images/phone.png');

  /// File path: assets/images/play.png
  AssetGenImage get play => const AssetGenImage('assets/images/play.png');

  /// File path: assets/images/popup_error.png
  AssetGenImage get popupError =>
      const AssetGenImage('assets/images/popup_error.png');

  /// File path: assets/images/popup_success.png
  AssetGenImage get popupSuccess =>
      const AssetGenImage('assets/images/popup_success.png');

  /// File path: assets/images/premium_bg.png
  AssetGenImage get premiumBgPng =>
      const AssetGenImage('assets/images/premium_bg.png');

  /// File path: assets/images/premium_bg.svg
  SvgGenImage get premiumBgSvg =>
      const SvgGenImage('assets/images/premium_bg.svg');

  /// File path: assets/images/premium_bg_icon.png
  AssetGenImage get premiumBgIcon =>
      const AssetGenImage('assets/images/premium_bg_icon.png');

  /// File path: assets/images/premium_bg_item.png
  AssetGenImage get premiumBgItem =>
      const AssetGenImage('assets/images/premium_bg_item.png');

  /// File path: assets/images/premium_br.png
  AssetGenImage get premiumBr =>
      const AssetGenImage('assets/images/premium_br.png');

  /// File path: assets/images/premium_diamon.png
  AssetGenImage get premiumDiamon =>
      const AssetGenImage('assets/images/premium_diamon.png');

  /// File path: assets/images/premium_header.png
  AssetGenImage get premiumHeader =>
      const AssetGenImage('assets/images/premium_header.png');

  /// File path: assets/images/present.svg
  SvgGenImage get present => const SvgGenImage('assets/images/present.svg');

  /// File path: assets/images/question.svg
  SvgGenImage get question => const SvgGenImage('assets/images/question.svg');

  /// File path: assets/images/question_detail_chart_bg.png
  AssetGenImage get questionDetailChartBg =>
      const AssetGenImage('assets/images/question_detail_chart_bg.png');

  /// File path: assets/images/quiz_icon_high.png
  AssetGenImage get quizIconHigh =>
      const AssetGenImage('assets/images/quiz_icon_high.png');

  /// File path: assets/images/quiz_icon_low.png
  AssetGenImage get quizIconLow =>
      const AssetGenImage('assets/images/quiz_icon_low.png');

  /// File path: assets/images/quiz_image.png
  AssetGenImage get quizImage =>
      const AssetGenImage('assets/images/quiz_image.png');

  /// File path: assets/images/radiobox_green.svg
  SvgGenImage get radioboxGreen =>
      const SvgGenImage('assets/images/radiobox_green.svg');

  /// File path: assets/images/radiobox_red.svg
  SvgGenImage get radioboxRed =>
      const SvgGenImage('assets/images/radiobox_red.svg');

  /// File path: assets/images/redClose.png
  AssetGenImage get redClose =>
      const AssetGenImage('assets/images/redClose.png');

  /// File path: assets/images/report_success.png
  AssetGenImage get reportSuccess =>
      const AssetGenImage('assets/images/report_success.png');

  /// File path: assets/images/result_card_bg.png
  AssetGenImage get resultCardBg =>
      const AssetGenImage('assets/images/result_card_bg.png');

  /// File path: assets/images/result_card_item.png
  AssetGenImage get resultCardItem =>
      const AssetGenImage('assets/images/result_card_item.png');

  /// File path: assets/images/result_card_item_1.png
  AssetGenImage get resultCardItem1 =>
      const AssetGenImage('assets/images/result_card_item_1.png');

  /// File path: assets/images/result_card_item_2.png
  AssetGenImage get resultCardItem2 =>
      const AssetGenImage('assets/images/result_card_item_2.png');

  /// File path: assets/images/result_page_item.png
  AssetGenImage get resultPageItem =>
      const AssetGenImage('assets/images/result_page_item.png');

  /// File path: assets/images/result_question_bookmark.svg
  SvgGenImage get resultQuestionBookmark =>
      const SvgGenImage('assets/images/result_question_bookmark.svg');

  /// File path: assets/images/result_screen_bg.png
  AssetGenImage get resultScreenBg =>
      const AssetGenImage('assets/images/result_screen_bg.png');

  /// File path: assets/images/review_corner.png
  AssetGenImage get reviewCorner =>
      const AssetGenImage('assets/images/review_corner.png');

  /// File path: assets/images/review_empty.png
  AssetGenImage get reviewEmpty =>
      const AssetGenImage('assets/images/review_empty.png');

  /// File path: assets/images/reward_bg_icon.png
  AssetGenImage get rewardBgIcon =>
      const AssetGenImage('assets/images/reward_bg_icon.png');

  /// File path: assets/images/right_chart_image.png
  AssetGenImage get rightChartImage =>
      const AssetGenImage('assets/images/right_chart_image.png');

  /// File path: assets/images/round_circle.svg
  SvgGenImage get roundCircle =>
      const SvgGenImage('assets/images/round_circle.svg');

  /// File path: assets/images/setting_bg_icon.png
  AssetGenImage get settingBgIcon =>
      const AssetGenImage('assets/images/setting_bg_icon.png');

  /// File path: assets/images/star.png
  AssetGenImage get star => const AssetGenImage('assets/images/star.png');

  /// File path: assets/images/stat_book.png
  AssetGenImage get statBook =>
      const AssetGenImage('assets/images/stat_book.png');

  /// File path: assets/images/stat_chart.png
  AssetGenImage get statChart =>
      const AssetGenImage('assets/images/stat_chart.png');

  /// File path: assets/images/stat_clock.png
  AssetGenImage get statClock =>
      const AssetGenImage('assets/images/stat_clock.png');

  /// File path: assets/images/stat_peace.png
  AssetGenImage get statPeace =>
      const AssetGenImage('assets/images/stat_peace.png');

  /// File path: assets/images/stat_pin.png
  AssetGenImage get statPin =>
      const AssetGenImage('assets/images/stat_pin.png');

  /// File path: assets/images/study_easier.png
  AssetGenImage get studyEasier =>
      const AssetGenImage('assets/images/study_easier.png');

  /// File path: assets/images/success_popup_icon.png
  AssetGenImage get successPopupIcon =>
      const AssetGenImage('assets/images/success_popup_icon.png');

  /// File path: assets/images/support_banner_icon.png
  AssetGenImage get supportBannerIcon =>
      const AssetGenImage('assets/images/support_banner_icon.png');

  /// File path: assets/images/tag.png
  AssetGenImage get tag => const AssetGenImage('assets/images/tag.png');

  /// File path: assets/images/ticket.png
  AssetGenImage get ticket => const AssetGenImage('assets/images/ticket.png');

  /// File path: assets/images/vi_flag.png
  AssetGenImage get viFlag => const AssetGenImage('assets/images/vi_flag.png');

  /// File path: assets/images/warning.svg
  SvgGenImage get warning => const SvgGenImage('assets/images/warning.svg');

  /// File path: assets/images/write.png
  AssetGenImage get write => const AssetGenImage('assets/images/write.png');

  /// List of all assets
  List<dynamic> get values => [
        a3dFire1,
        altArrowRight,
        altArrowUp,
        arrowRightDown,
        arrowRightUp,
        arrowRight,
        bg,
        bell,
        bolt,
        book1,
        bookPng,
        bookSvg,
        bookReview,
        bookmarkSquareMinimalisticExamResult,
        bookmarkSquareMinimalistic,
        bookmark,
        bookmarked,
        botomCloud,
        calendar,
        chart2Png,
        chart2Svg,
        chart3Png,
        chart3Svg,
        checkboxRedPng,
        checkboxRedSvg,
        checkmarkOnWhite,
        clockCircle,
        close,
        combo,
        contact,
        contact2,
        crownLine,
        danger,
        dangerReport,
        dialog,
        diplomaVerified,
        diploma,
        diplomaGrey,
        documentAdd,
        document,
        documentReviewHeader,
        documentsMinimalistic,
        fillSettings,
        finetuning,
        gift,
        global,
        houseReviewHeader,
        letter,
        lightbulbMinimalistic,
        lineSquareAcademicCap,
        notebookBookmark,
        notebook,
        notificationLinesRemove,
        palette,
        pointOnMap,
        questionReview,
        restartSquare,
        reviewHeader,
        settingUsersGroupTwoRounded,
        settingBook,
        settings1,
        settingsPng,
        settingsSvg,
        share,
        shieldPlus,
        sliderVertical,
        smartphone2,
        squareAcademicCap1,
        squareAcademicCapPng,
        squareAcademicCapSvg,
        starsMinimalistic,
        testReview,
        text,
        time,
        timeCalendar,
        topCloud,
        up,
        userRounded,
        usersGroupTwoRounded,
        alertPopupIcon,
        analyticBg1,
        analyticBg2,
        analyticBg3,
        analyticUnlockPre,
        arrowLeft,
        bgTour,
        bigLightning,
        blueFlag,
        board,
        bookIconHigh,
        crown,
        dart,
        deFlag,
        diamonComment,
        duration,
        edu,
        emptyIcon,
        enFlag,
        examFailed,
        examPassed,
        facebook1,
        formsReview,
        frFlag,
        fullTest,
        function,
        goldenStar,
        hand,
        hiFlag,
        homeLight1,
        homeLight2,
        icToast,
        image17,
        infoPopupIcon,
        instructionHeader,
        king1,
        language1,
        leftChartImage,
        light,
        lightning,
        line,
        luxury,
        mainChartImage,
        messenger1,
        miniTest,
        notes,
        onboardBg,
        openBook,
        passingScore,
        passrateFullBg,
        passrateSmallBg,
        pen,
        phone,
        play,
        popupError,
        popupSuccess,
        premiumBgPng,
        premiumBgSvg,
        premiumBgIcon,
        premiumBgItem,
        premiumBr,
        premiumDiamon,
        premiumHeader,
        present,
        question,
        questionDetailChartBg,
        quizIconHigh,
        quizIconLow,
        quizImage,
        radioboxGreen,
        radioboxRed,
        redClose,
        reportSuccess,
        resultCardBg,
        resultCardItem,
        resultCardItem1,
        resultCardItem2,
        resultPageItem,
        resultQuestionBookmark,
        resultScreenBg,
        reviewCorner,
        reviewEmpty,
        rewardBgIcon,
        rightChartImage,
        roundCircle,
        settingBgIcon,
        star,
        statBook,
        statChart,
        statClock,
        statPeace,
        statPin,
        studyEasier,
        successPopupIcon,
        supportBannerIcon,
        tag,
        ticket,
        viFlag,
        warning,
        write
      ];
}

class Assets {
  Assets._();

  static const $AssetsImagesGen images = $AssetsImagesGen();
}

class AssetGenImage {
  const AssetGenImage(this._assetName, {this.size = null});

  final String _assetName;

  final Size? size;

  Image image({
    Key? key,
    AssetBundle? bundle,
    ImageFrameBuilder? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? scale,
    double? width,
    double? height,
    Color? color,
    Animation<double>? opacity,
    BlendMode? colorBlendMode,
    BoxFit? fit,
    AlignmentGeometry alignment = Alignment.center,
    ImageRepeat repeat = ImageRepeat.noRepeat,
    Rect? centerSlice,
    bool matchTextDirection = false,
    bool gaplessPlayback = false,
    bool isAntiAlias = false,
    String? package,
    FilterQuality filterQuality = FilterQuality.low,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return Image.asset(
      _assetName,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      scale: scale,
      width: width,
      height: height,
      color: color,
      opacity: opacity,
      colorBlendMode: colorBlendMode,
      fit: fit,
      alignment: alignment,
      repeat: repeat,
      centerSlice: centerSlice,
      matchTextDirection: matchTextDirection,
      gaplessPlayback: gaplessPlayback,
      isAntiAlias: isAntiAlias,
      package: package,
      filterQuality: filterQuality,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
    );
  }

  ImageProvider provider({
    AssetBundle? bundle,
    String? package,
  }) {
    return AssetImage(
      _assetName,
      bundle: bundle,
      package: package,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}

class SvgGenImage {
  const SvgGenImage(
    this._assetName, {
    this.size = null,
  }) : _isVecFormat = false;

  const SvgGenImage.vec(
    this._assetName, {
    this.size = null,
  }) : _isVecFormat = true;

  final String _assetName;

  final Size? size;
  final bool _isVecFormat;

  SvgPicture svg({
    Key? key,
    bool matchTextDirection = false,
    AssetBundle? bundle,
    String? package,
    double? width,
    double? height,
    BoxFit fit = BoxFit.contain,
    AlignmentGeometry alignment = Alignment.center,
    bool allowDrawingOutsideViewBox = false,
    WidgetBuilder? placeholderBuilder,
    String? semanticsLabel,
    bool excludeFromSemantics = false,
    SvgTheme? theme,
    ColorFilter? colorFilter,
    Clip clipBehavior = Clip.hardEdge,
    @deprecated Color? color,
    @deprecated BlendMode colorBlendMode = BlendMode.srcIn,
    @deprecated bool cacheColorFilter = false,
  }) {
    return SvgPicture(
      _isVecFormat
          ? AssetBytesLoader(_assetName,
              assetBundle: bundle, packageName: package)
          : SvgAssetLoader(_assetName,
              assetBundle: bundle, packageName: package),
      key: key,
      matchTextDirection: matchTextDirection,
      width: width,
      height: height,
      fit: fit,
      alignment: alignment,
      allowDrawingOutsideViewBox: allowDrawingOutsideViewBox,
      placeholderBuilder: placeholderBuilder,
      semanticsLabel: semanticsLabel,
      excludeFromSemantics: excludeFromSemantics,
      theme: theme,
      colorFilter: colorFilter ??
          (color == null ? null : ColorFilter.mode(color, colorBlendMode)),
      clipBehavior: clipBehavior,
      cacheColorFilter: cacheColorFilter,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}
