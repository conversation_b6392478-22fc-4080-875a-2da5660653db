import 'package:flutter/material.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';
import 'package:syncfusion_flutter_core/theme.dart';
import 'package:syncfusion_flutter_sliders/sliders.dart';
import 'dart:math' as math;

class SliderWidget extends StatefulWidget {
  const SliderWidget(
      {super.key,
      required this.value,
      required this.onValueChange,
      required this.min,
      required this.max,
      required this.interval,
      required this.stepSize});

  final double value;
  final double min;
  final double max;
  final double interval;
  final double stepSize;
  final void Function(double)? onValueChange;

  @override
  State<SliderWidget> createState() => _SliderWidgetState();
}

class _SliderWidgetState extends State<SliderWidget> {
  late double _value;

  @override
  void initState() {
    _value = widget.value;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return SfSliderTheme(
      data: SfSliderThemeData(
          overlayColor: Colors.transparent,
          activeTrackHeight: 6,
          activeTrackColor: AppColors.baseBlue1,
          activeDividerColor: Colors.white,
          activeDividerRadius: 10,
          activeDividerStrokeColor: AppColors.border1st,
          activeDividerStrokeWidth: 1,
          inactiveTrackColor: AppColors.bgMain01,
          inactiveTrackHeight: 6,
          inactiveDividerRadius: 10,
          inactiveDividerColor: Colors.white,
          inactiveDividerStrokeColor: AppColors.border1st,
          inactiveDividerStrokeWidth: 1,
          activeLabelStyle:
              AppTextStyles.smallMedium.copyWith(color: AppColors.text1st),
          inactiveLabelStyle:
              AppTextStyles.smallMedium.copyWith(color: AppColors.text1st)),
      child: SfSlider(
        min: widget.min,
        max: widget.max,
        interval: widget.interval,
        stepSize: widget.stepSize,
        value: _value,
        showLabels: true,
        showDividers: true,
        thumbShape: _SfThumbShape(),
        onChanged: (value) {
          setState(() {
            _value = value;
            widget.onValueChange?.call(value);
          });
        },
      ),
    );
  }
}

class _SfThumbShape extends SfThumbShape {
  @override
  void paint(PaintingContext context, Offset center,
      {required RenderBox parentBox,
      required RenderBox? child,
      required SfSliderThemeData themeData,
      SfRangeValues? currentValues,
      dynamic currentValue,
      required Paint? paint,
      required Animation<double> enableAnimation,
      required TextDirection textDirection,
      required SfThumb? thumb}) {
    Size size = Size(28, 28);
    final paint = Paint();

    // Radius of the circle
    final radius = math.min(size.width, size.height) / 2;

    final shadowPaint = Paint();
    shadowPaint.color = Colors.black.withOpacity(0.2);
    shadowPaint.maskFilter =
        MaskFilter.blur(BlurStyle.normal, 4); // Apply blur for softness

    // Draw the shadow slightly offset for depth perception
    final shadowOffset =
        Offset(0, 1); // Adjust offset values for shadow position
    context.canvas.drawCircle(center + shadowOffset, radius, shadowPaint);

    // Draw the white circle
    paint.color = Colors.white;
    context.canvas.drawCircle(center, radius, paint);

    // Draw the blue circle a little bit smaller than the white circle
    paint.color = Colors.blue;
    context.canvas.drawCircle(center, radius - 7, paint);
  }
}
