import 'package:scrumpass_exam_simulator/data/models/enum/app_enum.dart';
import 'package:scrumpass_exam_simulator/domain/entities/app_data.dart';

class Constants {
  static const String appName = 'PSM Exam Pass';
  static const String certName = 'PSM-I';
  static const String appId = 'com.exampass.psm';
  static const String key =
      'YlhsZllYQndiR2xqWVhScGIyNWZjMlZqY21WMDE2NDUxOTMwNTg4OTU=';

  static const String amplitudeApiKey = '7ad2eeba2bb7ab3c3c8e139956ea5bec';

  // Settings Page
  static const String facebook = 'https://www.facebook.com/exampasscom';
  static const String facebookGroup =
      'https://www.facebook.com/share/g/12LTM9Ssm6r/';
  static const String messenger = 'https://m.me/exampasscom/';
  static const String phone = '+84945694499';
  static const String website = 'https://exam-pass.com/';
  static const String policy = 'https://exam-pass.com/privacy-policy';
  static const String terms = 'https://exam-pass.com/terms';
  static const String mail = '<EMAIL>';
  static const String appStoreId = '6741474550';
  static const String webUrl = "https://exam-pass.com/";

  // Premium Page
  static const String revenueCatIos = "appl_uIyKACtUmKjBsCOMIdvnSFAEuJq";
  static const String revenueCatAndroid = "goog_qHtJmsvvgPrGspCjtNjOWPnGURb";
  static const double monthlyOldPrice = 0.0;
  static const double quarterlyOldPrice = 0.0;
  static const double yearlyOldPrice = 0.0;

  static const List examOptionAlphabet = [
    'A. ',
    'B. ',
    'C. ',
    'D. ',
    'E. ',
    'F. ',
    'G. ',
    'H. ',
    'I. ',
    'J. ',
    'K. ',
    'L. ',
    'M. ',
    'N. ',
    'O. ',
    'P. ',
    'Q. ',
    'R. ',
    'S. ',
    'T. ',
    'U. ',
    'V. ',
    'W. ',
    'X. ',
    'Y. ',
    'Z. ',
  ];
  static const String fakeText =
      "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Donec vel egestas dolor, nec dignissim metus. Donec augue elit, rhoncus ac sodales id, porttitor vitae est. Donec laoreet rutrum libero sed pharetra.";
}

class EventLogConstants {
  static const String learningScreen = 'home';
  static const String settingsScreen = 'settings';

  // Event Name
  // Learning Screen
  static const String clickChooseATestDate = 'click_choose_a_test_date';
  static const String viewDetailStreak = 'view_detail_streak';
  static const String viewDetailPassingRate = 'view_detail_passing_rate';
  static const String clickPremiumBanner = 'click_premium_banner';
  static const String clickMockTest = 'click_mock_test';
  static const String clickPractice = 'click_practice';
  static const String clickTestInProgress = 'click_test_in_progress';
  static const String clickQuickTest = 'click_quick_test';
  static const String clickMissedQuestionTest = 'click_missed_question_test';
  static const String clickBookmarkQuestionTest =
      'click_bookmark_question_test';
  static const String clickDomainSubjectTest = 'click_domain_subject_test';
  static const String clickFlashcard = 'click_flashcard';
  static const String clickMiniTest = 'click_mini_test';
  static const String clickFullTest = 'click_full_test';
  static const String clickGetRewardBanner = 'click_get_reward_banner';
  static const String clickOtherCerts = 'click_other_certs';
  static const String clickBtnConfirm = 'click_btn_confirm';
  static const String btm_choosenumberofquestion = 'btm_choosenumberofquestion';
  static const String btm_chooseatestdate = 'btm_chooseatestdate';
  static const String btm_chooseaquestionbank = 'btm_chooseaquestionbank';

  // Settings Screen
  static const String clickName = 'click_name';
  static const String clickTrainingMode = 'click_training_mode';
  static const String clickLanguage = 'click_language';
  static const String clickTextSize = 'click_text_size';
  static const String turnOffStudyReminder = 'turn_off_study_reminder';
  static const String turnOnStudyReminder = 'turn_on_study_reminder';
  static const String clickSupportBanner = 'click_support_banner';
  static const String clickAboutUs = 'click_about_us';
  static const String clickRatingApp = 'click_rating_app';
  static const String clickSharingApp = 'click_sharing_app';
  static const String clickPrivacyPolicy = 'click_privacy_policy';
  static const String clickVersion = 'click_version';
  static const String clickRepeat = 'click_repeat';
  static const String viewSocialMedia = 'click_version';
  static const String viewAccountStatus = 'view_account_status';
  static const String typeName = 'type_name';
  static const String selectDailyRepeat = 'select_daily_repeat';
  static const String selectDay = 'select_day';
  static const String selectPracticeMode = 'select_practice_mode';
  static const String selectQuickPracticeMode = 'select_quick_practice_mode';
  static const String selectSimulateTheExam = 'select_simulate_the_exam';
  static const String selectTypeLanguage = 'select_type_language';
  static const String selectTypeSize = 'select_type_size';
  static const String btm_changename = 'btm_changename';
  static const String btm_setreminder = 'btm_setreminder';
  static const String btm_choosereviewmode = 'btm_choosereviewmode';
  static const String btm_chooselanguage = 'btm_chooselanguage';
  static const String btm_choosefontsize = 'btm_choosefontsize';

  static const String subscriptionSuccess = 'pay_subscription_succes';
}

// Global variables
Environment environment = Environment.prod;

class Global {
  static String _username = '';
  static bool isPremium = false;
  static bool isTrial = false;
  static bool _firstLaunch = false;
  static bool _finishPrimaryTour = false;
  static bool _finishTourReview = false;
  static bool _finishTourMock = false;
  static bool _finishTourAnalyst = false;
  static double _examFontSize = 18;
  static DateTime? _testDate;
  static Language locale = Language.en;
  static SubscriptionType? _subscriptionType;
  static String appVersion = '3.0.0';

  static String get getUsername => _username;
  static DateTime? get getTestDate => _testDate;
  static bool get getStateLaunch => _firstLaunch;
  static bool get getStatePrimaryTour => _finishPrimaryTour;
  static bool get getStateTourReview => _finishTourReview;
  static bool get getStateTourMock => _finishTourMock;
  static bool get getStateTourAnalyst => _finishTourAnalyst;
  static double get getExamFontSize => _examFontSize;
  static SubscriptionType? get getSubscriptionType => _subscriptionType;
  static void updateUsername(String username) {
    _username = username;
  }

  static void updateTestDate(DateTime? date) {
    _testDate = date;
  }

  static void updateLaunchState(bool state) {
    _firstLaunch = state;
  }

  static void completeTourState() {
    _finishPrimaryTour = true;
    _finishTourReview = true;
    _finishTourMock = true;
    _finishTourAnalyst = true;
  }

  static void updateTourStateAppData(AppData appData) {
    updateTourPrimaryState(appData.finishPrimaryTour);
    updateTourReviewState(appData.finishTourReview);
    updateTourMockState(appData.finishTourMock);
    updateTourAnalystState(appData.finishTourAnalyst);
  }

  static void updateTourReviewState(bool state) {
    _finishTourReview = state;
  }

  static void updateTourMockState(bool state) {
    _finishTourMock = state;
  }

  static void updateTourPrimaryState(bool state) {
    _finishPrimaryTour = state;
  }

  static void updateTourAnalystState(bool state) {
    _finishTourAnalyst = state;
  }

  static void completeTourAnalyst() {
    _finishTourAnalyst = true;
  }

  static void updateExamFontSize(double fontSize) {
    _examFontSize = fontSize;
  }

  static void updateSubscriptionType(SubscriptionType? subscriptionType) {
    _subscriptionType = subscriptionType;
  }

  static int totalQuestion = 1000;
}

enum SubscriptionType { monthly, quarterly, halfYearly, yearly }

extension SubscriptionTypeExt on SubscriptionType {
  String get displayText {
    switch (this) {
      case SubscriptionType.monthly:
        return "1 month";
      case SubscriptionType.quarterly:
        return "3 months";
      case SubscriptionType.halfYearly:
        return "6 months";
      case SubscriptionType.yearly:
        return "12 months";
    }
  }
}

String get apiUrl => environment.url;
String get apiUrlPremium => environment.urlPremium;

enum Environment { dev, prod, prodBackup, local }

extension EnvironmentExt on Environment {
  String get url {
    switch (this) {
      case Environment.dev:
        return "https://dev.scrumpass.com/v3/api/";
      case Environment.prod:
        return "https://exam.scrumpass.com/v3/api/";
      case Environment.prodBackup:
        return "https://exam-pass.com/v3/api/";
      case Environment.local:
        return "http://************/scrumpass-exam/v3/api/";
    }
  }

  String get urlPremium {
    switch (this) {
      case Environment.dev:
        return "https://dev.scrumpass.com/v3/api_premium/";
      case Environment.prod:
        return "https://exam.scrumpass.com/v3/api_premium/";
      case Environment.prodBackup:
        return "https://exam-pass.com/v3/api_premium/";
      case Environment.local:
        return "http://localhost/scrumpass-exam/v3/api_premium/";
    }
  }
}

extension StringCasingExtension on String {
  String get toCapitalized =>
      length > 0 ? '${this[0].toUpperCase()}${substring(1).toLowerCase()}' : '';
  String get toTitleCase => replaceAll(RegExp(' +'), ' ')
      .split(' ')
      .map((str) => str.toCapitalized)
      .join(' ');
}
