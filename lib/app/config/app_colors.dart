import 'package:flutter/material.dart';
import 'package:scrumpass_exam_simulator/app/extensions/color.dart';

class AppColors {
  static Color primary = HexColor("1B82DF");
  static Color lightGray = HexColor("D3D3D3");
  static Color textHint = HexColor("667085");
  static Color textTitle = HexColor("101828");
  static Color text1st = HexColor("101828");
  static Color text2nd = HexColor("1D2939");
  static Color passingRateBorder = HexColor("8FCAFF");
  static Color progressBarBg = HexColor("27547D");
  static Color passRateStatisticsBg = HexColor("659ED2");
  static Color textPremiumTag = HexColor("232323");
  static Color divider = HexColor("EAECF0");
  static Color textBtn1st = HexColor("FFFFFF");
  static Color textBtn2nd = HexColor("0673C6");
  static Color btn1stDefault = HexColor("0673C6");
  static Color btn2ndDefault = HexColor("E6F2FB");
  static Color bgMain01 = HexColor("EAECF0");
  static Color bgMain02 = HexColor("D0D5DD");
  static Color border1st = HexColor("D0D5DD");
  static Color borderLine = HexColor("EAECF0");
  static Color icDefault = HexColor("1D2939");
  static Color btnDisabled = HexColor("D0D5DD");
  static Color textBtnDisabled = HexColor("667085");
  static Color icBtn2ndDefault = HexColor("0673C6");
  static Color icHint = HexColor("667085");
  static Color textDisabled = HexColor("8C9093");
  static Color bgWhite = HexColor("FFFFFF");
  static Color white = HexColor("FFFFFF");
  static Color textInverseTitle = HexColor("FFFFFF");
  static Color textInverse2nd = HexColor("D0D5DD");
  static Color icInverse = HexColor("FFFFFF");
  static Color bgContactBtn = HexColor("232323");
  static Color bgSupportBanner = HexColor("F3F1E2");
  static Color getRewardTextBtn = HexColor("232323");
  static Color baseBlue6 = HexColor("0A6DE1");
  static Color baseBlue5 = HexColor("077EDA");
  static Color baseBlue4 = HexColor("3998E1");
  static Color baseBlue3 = HexColor("59A9E6");
  static Color baseBlue2 = HexColor("B2D7F4");
  static Color baseBlue1 = HexColor("E6F2FB");
  static Color baseBlueBorder = HexColor("1B82DF");
  static Color selectionTitle = HexColor("344054");
  static Color yellow1 = HexColor("FFF9E6");
  static Color yellow5 = HexColor("FFC400");
  static Color green1 = HexColor("E9F6ED");
  static Color green2 = HexColor("9AD4AB");
  static Color green3 = HexColor("6CC084");
  static Color green5 = HexColor("24A148");
  static Color green6 = HexColor("219342");
  static Color green7 = HexColor("367E4B");
  static Color aboutUsContent = HexColor("666666");
  static Color tabbarBg = HexColor("F9FEFF");
  static Color getRewardBg = HexColor("232323");
  static Color premiumBannerBg = HexColor("232323");
  static Color timeRowText = HexColor("4D5358");
  static Color examBoxHeader = HexColor("EAECF0");
  static Color examBoxHeaderTitle = HexColor("1F1F1F");
  static Color examQuestion = HexColor("1F1F1F");
  static Color examDivider = HexColor("E8E8E8");
  static Color examOptionAlphabet = HexColor("1F1F1F");
  static Color examOption = HexColor("1F1F1F");
  static Color elementTextNormal = HexColor("091E42");
  static Color yellow3 = HexColor("FFD754");
  static Color bgSuccess = HexColor("219342");
  static Color questionListIndex = HexColor("344054");
  static Color red1 = HexColor("FEECEB");
  static Color red2 = HexColor("F8A9A3");
  static Color red3 = HexColor("F5827A");
  static Color red5 = HexColor("FF040F");
  static Color icSuccess = HexColor("219342");
  static Color textActive = HexColor("0673C6");
  static Color popupTitle = HexColor("505050");
  static Color bgInfoSurface = HexColor("E6F2FB");
  static Color neutral9 = HexColor("1D2939");
  static Color neutral5 = HexColor("98A2B3");
  static Color neutral2 = HexColor("E6EAEF");
  static Color neutral1 = HexColor("F9FAFB");
  static Color resultAppbarTitle = HexColor("000000");
  static Color resultCorrect = HexColor("97EC9A");
  static Color textLink = HexColor("077EDA");
  static Color examBg = HexColor("FAFAFA");
  static Color benefit1 = HexColor("FFE9E8");
  static Color benefit2 = HexColor("FFF5E5");
  static Color benefit3 = HexColor("E5F2FF");
  static Color benefit4 = HexColor("F0E5FF");
  static Color subcriptionItemBg = HexColor("F9FAFB");
  static Color selectedSubcriptionItemBg = HexColor("FFF9E6");
  static Color selectedSubcriptionItemBorder = HexColor("FFE387");
  static Color selectedSubcriptionItemTag = HexColor("FFC400");
  static Color notSelectedSubcriptionItemTag = HexColor("98A2B3");
  static Color textWhite = HexColor("FFFFFF");
  static Color premiumHeaderbg = HexColor("AED4F6");
  static Color premiumFooterbg = HexColor("F9FEFF");
  static Color examFooterBg = HexColor("F9FEFF");
  static Color resultItemCorrectBg = HexColor("F8FCFA");
  static Color resultItemCorrectBorder = green3;
  static Color resultItemInCorrectBg = HexColor("FFF9F9");
  static Color resultItemInCorrectBorder = red3;
  static Color resultOverall = HexColor("0569FF");
  static Color textLabel = HexColor("1D2939");
  static Color resultFooterBg = HexColor("F9FEFF");
  static Color quickTestBg = HexColor("E5FFEB");
  static Color missedTestBg = HexColor("FFE6DC");
  static Color bookmarkTestBg = HexColor("D9F1FF");
  static Color domainTestBg = HexColor("EEECFC");
  static Color fullTestBg = HexColor("EEECFC");
  static Color miniTestBg = HexColor("E4FDFB");
  static Color bgSuccessSurface = HexColor("E9F6ED");
  static Color textSuccess = HexColor("219342");
  static Color bgErrorSurface = HexColor("FEECEB");
  static Color textError = HexColor("DA3E33");
  static Color orange4 = HexColor("F9A63A");
  static Color orange5 = HexColor("F79009");
  static Color orange6 = HexColor("F9A63A");
  static Color brown8 = HexColor("5D4838");
  static Color brown2 = HexColor("D6CEB2");
  static Color radarGreen = HexColor("50B46D");
  static Color bgWarningSurface = HexColor("FEF4E6");
  static Color textWarning = HexColor("E18308");
  static Color mockProgressBarBg = HexColor("E3E5E5");
  static Color mockScorePassText = HexColor("43B14A");
  static Color mockScoreFailText = HexColor("DA3E33");
  static Color textInfo = HexColor("0673C6");
  static Color placeholder = HexColor("959595");
  static List<Color> passrateGradient = [
    HexColor("409DF0"),
    HexColor("006BCB")
  ];
  static List<Color> resutltCardGradient = [
    HexColor("3594EA"),
    HexColor("3594EA"),
  ];
  static List<Color> passrateGradientBorder = [
    HexColor("8FCAFF"),
    HexColor("8FCAFF"),
  ];
  static List<Color> blueGradientProgress = [
    HexColor("41C6FF"),
    HexColor("D5F2FF")
  ];
  static List<Color> yellowGradientProgress = [
    HexColor("FFE141"),
    HexColor("FFFBD5")
  ];
  static List<Color> greenGradientProgress = [
    HexColor("41FF5F"),
    HexColor("D5FFE3")
  ];
  static List<Color> premiumTagGradient = [
    HexColor("E5DE96"),
    HexColor("FFF8B9")
  ];
  static List<Color> examAppbarGradient = [
    HexColor("409DF0"),
    HexColor("006BCB")
  ];
  static List<Color> premiumPageGradient = [
    HexColor("55A5ED"),
    HexColor("FFFFFF")
  ];
  static List<Color> premiumBenefitGradient = [
    HexColor("525252"),
    HexColor("232323")
  ];

  static List<Color> premiumBenefitDividerGradient = [
    HexColor("EAECF0"),
    HexColor("828488").withOpacity(0.5)
  ];
  static List<Color> selectedSubcriptionGradient = [
    HexColor("EAECF0"),
    HexColor("828488").withOpacity(0.5)
  ];
  static List<Color> reviewHeaderGradient = [
    HexColor("409DF0"),
    HexColor("006BCB")
  ];
  static List<Color> analyticsBgGradient = [
    HexColor("4386EB"),
    HexColor("F8FDFF")
  ];
  static List<Color> analyticsScoreChartGradient = [
    HexColor("FFFFFF"),
    HexColor("FFFFFF")
  ];
  static List<Color> analyticsKnowledgeGradient = [
    HexColor("FFF8E1"),
    HexColor("FFFBEF")
  ];
  static List<Color> analyticsQuizDetailGradient = [
    HexColor("D5F8C7"),
    HexColor("F3FFEE")
  ];
  static List<Color> analyticsQuestionDetailGradient = [
    HexColor("FFEED7"),
    HexColor("FFF9F0")
  ];
  static List<Color> analyticsQuestionDetailWrongGradient = [
    HexColor("EF817A"),
    HexColor("F36960")
  ];
  static List<Color> analyticsQuestionDetailMarkedGradient = [
    HexColor("FFDF75"),
    HexColor("FFC916")
  ];
  static List<Color> analyticsTimeDetailGradient = [
    HexColor("D4EDFF"),
    HexColor("F1F9FF")
  ];
  static List<Color> questionDetailWrongGradient = [
    HexColor("F36960"),
    HexColor("EF817A")
  ];
  static List<Color> questionDetailMarkedGradient = [
    HexColor("FFC916"),
    HexColor("FFDF75")
  ];
  static List<Color> quizDetailChartGradient = [
    HexColor("85D79C"),
    HexColor("ECF6E8")
  ];
  static List<Color> analyticPremiumGradient = [
    HexColor("232323"),
    HexColor("444444")
  ];
  static List<Color> mockFailBgGradient = [
    HexColor("FEEDEB"),
    HexColor("FFFFFF")
  ];
  static List<Color> mockFailBorderGradient = [
    HexColor("F8A9A3"),
    HexColor("FFFFFF")
  ];
  static List<Color> mockPassBgGradient = [
    HexColor("E3FFF2"),
    HexColor("FFFFFF")
  ];
  static List<Color> mockPassBorderGradient = [
    HexColor("9AD4AB"),
    HexColor("FFFFFF")
  ];
  static List<Color> errorPopupBgGradient = [
    HexColor("FFEAEA"),
    HexColor("FFFFFF")
  ];
  static List<Color> successPopupBgGradient = [
    HexColor("ECFFEA"),
    HexColor("FFFFFE")
  ];
  static List<Color> successReportBgGradient = [
    HexColor("FFF6E9"),
    HexColor("FFFFFF")
  ];
  static List<Color> generalBgHeaderGradient = [
    HexColor("b0d5f7"),
    HexColor("FFFFFF")
  ];
  static List<Color> resultBgHeaderGradient = [
    HexColor("A9D2F6"),
    HexColor("FFFFFF")
  ];
  static List<Color> homeGradient = [
    HexColor("E5F2F8"),
    HexColor("E9F0F3"),
    HexColor("FEFEFE"),
  ];
  static List<Color> productTourGradient = [
    HexColor("369FFF"),
    HexColor("0372C7"),
  ];
  static List<Color> tagWelcomeGradient = [
    HexColor("2D84D4"),
    HexColor("438ED4"),
  ];
  static Color stickyNavBarBg = HexColor("c6e1f9");
  static Color onbroadPrimaryBg = HexColor("1B82DF");
  static Color onbroadSecondaryBg = HexColor("58B0FF");
  static Color grey = HexColor("DCDCDC");
}

class BoxShadows {
  static List<BoxShadow> settingBoxShadow = [
    BoxShadow(
        color: Colors.black.withOpacity(0.04),
        offset: Offset(0, 4),
        blurRadius: 16)
  ];
}
