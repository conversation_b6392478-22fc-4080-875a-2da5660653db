import 'package:expandable/expandable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';
import 'package:scrumpass_exam_simulator/app/extensions/color.dart';
import 'package:scrumpass_exam_simulator/generated/assets.gen.dart';
import 'package:scrumpass_exam_simulator/generated/locales.g.dart';

class BenefitBox extends StatelessWidget {
  const BenefitBox({super.key});

  @override
  Widget build(BuildContext context) {
    return Flexible(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: DecoratedBox(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: AppColors.premiumBenefitGradient,
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
            boxShadow: [
              BoxShadow(
                  color: HexColor('ABD2F6').withOpacity(0.24),
                  offset: const Offset(0, 1),
                  blurRadius: 20)
            ],
            borderRadius: BorderRadius.circular(12),
          ),
          child: Stack(
            children: [
              Positioned(
                bottom: 0,
                right: 0,
                child: Assets.images.premiumBgSvg.svg(),
              ),
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    BenefitItem(
                      iconPath: Assets.images.gift.path,
                      iconBgColor: AppColors.benefit1,
                      title: '1000+ ${LocaleKeys.questions.tr}',
                      description:
                          '${LocaleKeys.lastUpdatedTo.tr} ${DateTime.now().year}',
                    ),
                    BenefitItem(
                      iconPath: Assets.images.documentAdd.path,
                      iconBgColor: AppColors.benefit2,
                      title: LocaleKeys.benefitItemTitle2.tr,
                      description: LocaleKeys.benefitItemDesc2.tr,
                    ),
                    BenefitItem(
                      iconPath: Assets.images.pointOnMap.path,
                      iconBgColor: AppColors.benefit3,
                      title: LocaleKeys.benefitItemTitle3.tr,
                      description: LocaleKeys.benefitItemDesc3.tr,
                    ),
                    BenefitItem(
                      iconPath: Assets.images.contact2.path,
                      iconBgColor: AppColors.benefit4,
                      title: LocaleKeys.benefitItemTitle4.tr,
                      description: LocaleKeys.benefitItemDesc4.tr,
                    ),
                    const SizedBox(height: 12),
                    BenefitDetails(),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class BenefitDetails extends StatefulWidget {
  const BenefitDetails({super.key});

  @override
  State<BenefitDetails> createState() => _BenefitDetailsState();
}

class _BenefitDetailsState extends State<BenefitDetails> {
  final ExpandableController controller = ExpandableController();
  double turns = 0.0;

  void _changeRotation() {
    setState(() {
      if (turns == 0.0) {
        turns += 0.5;
      } else {
        turns -= 0.5;
      }
    });
  }

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Expandable(
            controller: controller,
            collapsed: const SizedBox(),
            expanded: Column(
              children: [
                _BenefitDetailDivider(),
                const SizedBox(height: 16),
                _DetailHeader(
                  iconPath: Assets.images.crown.path,
                  text: LocaleKeys.premiumFeatures.tr,
                ),
                const SizedBox(height: 8),
                _DetailItem(text: LocaleKeys.premiumFeatures1.tr),
                _DetailItem(text: LocaleKeys.premiumFeatures2.tr),
                _DetailItem(text: LocaleKeys.premiumFeatures3.tr),
                _DetailItem(text: LocaleKeys.premiumFeatures4.tr),
                const SizedBox(height: 16),
                _BenefitDetailDivider(),
                const SizedBox(height: 16),
                _DetailHeader(
                  iconPath: Assets.images.star.path,
                  text: LocaleKeys.premiumQuizModes.tr,
                ),
                const SizedBox(height: 8),
                _DetailItem(text: LocaleKeys.quickPractice.tr),
                _DetailItem(text: LocaleKeys.advancedPractice.tr),
                _DetailItem(text: LocaleKeys.missedQuestionPractice.tr),
                _DetailItem(text: LocaleKeys.savedQuestionPractice.tr),
                _DetailItem(text: LocaleKeys.weakestDomainPractice.tr),
                const SizedBox(height: 16),
              ],
            )),
        GestureDetector(
          onTap: () {
            setState(() {
              controller.expanded = !controller.expanded;
              _changeRotation();
            });
          },
          child: Row(
            children: [
              Text(
                controller.expanded
                    ? LocaleKeys.collapse.tr
                    : LocaleKeys.benefitDetails.tr,
                style: AppTextStyles.smallSemiBold
                    .copyWith(color: AppColors.textInverseTitle),
              ),
              const SizedBox(width: 4),
              AnimatedRotation(
                turns: turns,
                duration: const Duration(milliseconds: 300),
                child: RotatedBox(
                    quarterTurns: 2,
                    child: Assets.images.altArrowUp
                        .svg(color: AppColors.textInverseTitle)),
              ),
            ],
          ),
        )
      ],
    );
  }
}

class _DetailItem extends StatelessWidget {
  const _DetailItem({required this.text});

  final String text;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 8),
      child: Row(
        children: [
          Assets.images.checkmarkOnWhite.svg(),
          const SizedBox(width: 8),
          Expanded(
              child: Text(
            text,
            style: AppTextStyles.smallRegular
                .copyWith(color: AppColors.textInverseTitle),
          ))
        ],
      ),
    );
  }
}

class _DetailHeader extends StatelessWidget {
  const _DetailHeader({
    required this.iconPath,
    required this.text,
  });

  final String iconPath;
  final String text;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Image.asset(iconPath, width: 20),
        const SizedBox(width: 8),
        Expanded(
            child: Text(
          text,
          style: AppTextStyles.smallBold
              .copyWith(color: AppColors.textInverseTitle),
        ))
      ],
    );
  }
}

class _BenefitDetailDivider extends StatelessWidget {
  const _BenefitDetailDivider({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return DecoratedBox(
      decoration: BoxDecoration(
          gradient:
              LinearGradient(colors: AppColors.premiumBenefitDividerGradient)),
      child: const SizedBox(
        height: 1,
        width: double.infinity,
      ),
    );
  }
}

class BenefitItem extends StatelessWidget {
  const BenefitItem(
      {super.key,
      required this.iconPath,
      required this.iconBgColor,
      required this.title,
      required this.description});

  final String iconPath;
  final Color iconBgColor;
  final String title;
  final String description;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Row(
        children: [
          SizedBox(
            width: 32,
            height: 32,
            child: DecoratedBox(
              decoration: BoxDecoration(
                color: iconBgColor,
                shape: BoxShape.circle,
              ),
              child: Center(
                child: SvgPicture.asset(iconPath),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppTextStyles.smallSemiBold
                      .copyWith(color: AppColors.textInverseTitle),
                ),
                const SizedBox(height: 2),
                Text(
                  description,
                  style: AppTextStyles.xSmallRegular
                      .copyWith(color: AppColors.textInverse2nd),
                ),
              ],
            ),
          )
        ],
      ),
    );
  }
}
