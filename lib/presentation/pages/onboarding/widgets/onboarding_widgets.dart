import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_constants.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';
import 'package:scrumpass_exam_simulator/generated/assets.gen.dart';

class OnboardingPages {
  Widget page1() {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 16, horizontal: 16),
      child: Assets.images.phone.image(
        fit: BoxFit.contain,
      ),
    );
  }

  Widget page2() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16.0),
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: 32),
        child: Assets.images.quizImage.image(fit: BoxFit.fitWidth),
      ),
    );
  }

  Widget page3() {
    return AbsorbPointer(
      child: Container(
        width: Get.mediaQuery.size.width > 550
            ? Get.mediaQuery.size.width * 0.6
            : Get.mediaQuery.size.width,
        padding: const EdgeInsets.symmetric(vertical: 32.0),
        child: CarouselSlider(
            items: [
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                child: Assets.images.leftChartImage.image(),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                child: Assets.images.mainChartImage.image(),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                child: Assets.images.rightChartImage.image(),
              ),
            ],
            options: CarouselOptions(
              aspectRatio: 1,
              viewportFraction: 0.7,
              initialPage: 0,
              enableInfiniteScroll: true,
              autoPlay: true,
              autoPlayInterval: Duration(milliseconds: 1800),
              autoPlayAnimationDuration: Duration(milliseconds: 800),
              enlargeStrategy: CenterPageEnlargeStrategy.height,
              autoPlayCurve: Curves.easeOutSine,
              enlargeCenterPage: true,
              enlargeFactor: 0.65,
              scrollDirection: Axis.horizontal,
              clipBehavior: Clip.hardEdge,
            )),
      ),
    );
  }

  Widget page4() {
    return Container(
      margin: EdgeInsets.only(top: 32),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Stack(
            alignment: Alignment.center,
            clipBehavior: Clip.none,
            children: [
              Positioned(
                top: -70,
                left: -20,
                child: customData(),
              ),
              Container(
                constraints: BoxConstraints(maxHeight: 380),
                padding: EdgeInsets.symmetric(
                  horizontal: Get.mediaQuery.size.width * 0.2,
                ),
                height: Get.mediaQuery.size.height *
                    (Get.mediaQuery.size.width > 550 ? 0.25 : 0.3),
                child: Assets.images.diamonComment.image(fit: BoxFit.contain),
              ),
              Positioned(
                top: 10,
                right: 0,
                child: Container(
                  height: 80,
                  child: Assets.images.formsReview.image(fit: BoxFit.fitHeight),
                ),
              ),
              Positioned(
                bottom: -40,
                left: 0,
                child: Container(
                  height: 80,
                  child: Assets.images.studyEasier.image(fit: BoxFit.fitHeight),
                ),
              )
            ],
          ),
        ],
      ),
    );
  }

  Widget customData() {
    return Row(
      children: [
        Container(
          margin: EdgeInsets.only(left: Get.mediaQuery.size.width * 0.1),
          padding: EdgeInsets.all(10),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: LinearGradient(
              colors: AppColors.tagWelcomeGradient,
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                height: 40,
                width: 40,
                child: Assets.images.present.svg(fit: BoxFit.contain),
              ),
              SizedBox(width: 10),
              Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "${Global.totalQuestion}+ questions",
                    style: AppTextStyles.smallBold.copyWith(
                      color: AppColors.white,
                    ),
                  ),
                  Text("Last updated to ${DateTime.now().year}",
                      style: AppTextStyles.smallRegular.copyWith(
                        color: AppColors.white,
                      )),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }
}
