import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';
import 'package:scrumpass_exam_simulator/app/widgets/button/primary_button.dart';
import 'package:scrumpass_exam_simulator/app/widgets/button/secondary_button.dart';
import 'package:scrumpass_exam_simulator/generated/assets.gen.dart';
import 'package:scrumpass_exam_simulator/generated/locales.g.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/result_detail/result_detail_controller.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/result/widgets/result_content.dart';

class ResultPage extends GetView<ResultDetailController> {
  const ResultPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: DecoratedBox(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: AppColors.resultBgHeaderGradient,
            stops: [0.0, 0.25],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: Stack(
          children: [
            Positioned(
              top: 0,
              right: 0,
              child: Assets.images.resultPageItem.image(width: 100),
            ),
            Column(
              children: [
                ResultAppBar(),
                ResultContent(),
                BottomButtons(controller: controller),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class BottomButtons extends StatelessWidget {
  const BottomButtons({
    super.key,
    required this.controller,
  });

  final ResultDetailController controller;

  @override
  Widget build(BuildContext context) {
    return DecoratedBox(
      decoration: BoxDecoration(color: AppColors.resultFooterBg),
      child: Padding(
        padding:
            const EdgeInsets.only(left: 16, right: 16, bottom: 50, top: 16),
        child: Row(
          children: [
            Flexible(
                flex: 1,
                child: SecondaryButton(
                    text: LocaleKeys.backToHome.tr,
                    onTap: () {
                      controller.backToHome();
                    })),
            const SizedBox(width: 16),
            Flexible(
              flex: 1,
              child: PrimaryButton(
                  text: LocaleKeys.tryAgain.tr,
                  onTap: () {
                    controller.onTryAgain();
                  }),
            ),
          ],
        ),
      ),
    );
  }
}

class ResultAppBar extends GetView<ResultDetailController> {
  const ResultAppBar({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        const SizedBox(height: 45),
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 16),
          child: Row(
            children: [
              controller.showBackBtn
                  ? GestureDetector(
                      child: Assets.images.arrowLeft
                          .svg(color: AppColors.resultAppbarTitle),
                      onTap: () => Get.back(),
                    )
                  : const SizedBox(width: 24),
              Expanded(
                  child: Text(
                LocaleKeys.examResult.tr,
                textAlign: TextAlign.center,
                style: AppTextStyles.largeBold
                    .copyWith(color: AppColors.resultAppbarTitle),
              )),
              const SizedBox(width: 24)
            ],
          ),
        ),
      ],
    );
  }
}
