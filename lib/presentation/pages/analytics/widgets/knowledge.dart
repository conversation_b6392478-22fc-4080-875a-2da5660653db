import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_constants.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';
import 'package:scrumpass_exam_simulator/app/extensions/color.dart';
import 'package:scrumpass_exam_simulator/generated/assets.gen.dart';
import 'package:scrumpass_exam_simulator/generated/locales.g.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/analytics/analytics_controller.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/analytics/widgets/statistic_card.dart';

class Knowledge extends GetView<AnalyticsController> {
  const Knowledge({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return StatisticCard(
      enableBlur: true,
      icon: Assets.images.statPeace.path,
      title: LocaleKeys.knowledgeTitle.tr,
      bgColor: AppColors.analyticsKnowledgeGradient,
      child: Column(
        children: [
          const SizedBox(
            height: 20,
          ),
          SizedBox(
            width: double.infinity,
            height: 220,
            child: RadarChart(
              RadarChartDataExtended(
                radarShape: RadarShape.polygon,
                dataSets: showingDataSets(),
                radarBackgroundColor: Colors.transparent,
                borderData: FlBorderData(show: false),
                radarBorderData: const BorderSide(color: Colors.transparent),
                titlePositionPercentageOffset: 0.25,
                titleTextStyle: AppTextStyles.xxSmallMedium
                    .copyWith(color: AppColors.brown8),
                getTitle: (index, angle) {
                  // thứ tự phải trùng với phía BE
                  switch (index) {
                    case 0:
                      return RadarChartTitle(
                          text: 'Scrum Event', positionPercentageOffset: 0.05);
                    case 1:
                      return RadarChartTitle(
                          text: 'Scrum Artifacts',
                          positionPercentageOffset: 0.35);
                    case 2:
                      return RadarChartTitle(
                        text: 'Scrum Team &\nOrganization',
                      );
                    case 3:
                      return RadarChartTitle(
                        text: 'Leadership &\nManagement Skill',
                      );
                    case 4:
                      return RadarChartTitle(
                        text: 'Product\nDevelopment\n& Management',
                      );

                    default:
                      return const RadarChartTitle(text: '');
                  }
                },
                tickCount: 5,
                ticksTextStyle:
                    const TextStyle(color: Colors.transparent, fontSize: 10),
                tickBorderData: const BorderSide(color: Colors.transparent),
                gridBorderData: BorderSide(color: AppColors.brown2, width: 1),
              ),
              swapAnimationDuration: const Duration(milliseconds: 400),
            ),
          ),
          const SizedBox(
            height: 30,
          ),
          _Bottom(),
          const SizedBox(
            height: 14,
          ),
        ],
      ),
    );
  }

  List<RadarDataSet> showingDataSets() {
    return rawDataSets().asMap().entries.map((entry) {
      final rawDataSet = entry.value;

      return RadarDataSet(
        fillColor: rawDataSet.color,
        borderColor: rawDataSet.borderColor,
        entryRadius: 0,
        dataEntries:
            rawDataSet.values.map((e) => RadarEntry(value: e)).toList(),
        borderWidth: 1,
      );
    }).toList();
  }

  List<RawDataSet> rawDataSets() {
    const int dataLength = 5;
    return [
      for (var i = 1; i <= 5; i++) ...{
        RawDataSet(
          title: 'Step Border',
          color: Colors.transparent,
          borderColor: AppColors.brown2,
          values: List.generate(dataLength, (_) => i.toDouble()),
        ),
      },
      RawDataSet(
        title: 'Điểm khuyến nghị',
        color: HexColor("50B46D").withOpacity(0.2),
        borderColor: AppColors.green5,
        values: List.filled(dataLength, 4),
      ),
      RawDataSet(
        title: 'Điểm của bạn',
        color: AppColors.orange4.withOpacity(0.33),
        borderColor: AppColors.orange4,
        values: Global.isPremium
            ? (controller.radarChartData.length == dataLength
                ? controller.radarChartData
                : List.filled(dataLength, 0))
            : List.filled(dataLength, 0),
      ),
    ];
  }
}

class _Bottom extends StatelessWidget {
  const _Bottom();

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        DecoratedBox(
            decoration: BoxDecoration(
                color: AppColors.orange4.withOpacity(0.33),
                borderRadius: BorderRadius.circular(4),
                border: Border.all(color: AppColors.orange4)),
            child: const SizedBox(width: 16, height: 16)),
        const SizedBox(width: 8),
        Text(
          LocaleKeys.yourScore.tr,
          style: AppTextStyles.xSmallMedium.copyWith(color: AppColors.orange6),
        ),
        const SizedBox(width: 40),
        DecoratedBox(
            decoration: BoxDecoration(
                color: AppColors.radarGreen.withOpacity(0.33),
                borderRadius: BorderRadius.circular(4),
                border: Border.all(color: AppColors.green5)),
            child: const SizedBox(width: 16, height: 16)),
        const SizedBox(width: 8),
        Text(
          LocaleKeys.recomendScore.tr,
          style: AppTextStyles.xSmallMedium.copyWith(color: AppColors.green6),
        ),
      ],
    );
  }
}

class RawDataSet {
  RawDataSet({
    required this.title,
    required this.color,
    required this.borderColor,
    required this.values,
  });

  final String title;
  final Color color;
  final Color borderColor;
  final List<double> values;
}

/// Fix zero value radar chart
class RadarChartDataExtended extends RadarChartData {
  RadarChartDataExtended({
    List<RadarDataSet>? dataSets,
    RadarShape? radarShape,
    BorderSide? radarBorderData,
    double? titlePositionPercentageOffset,
    TextStyle? titleTextStyle,
    GetTitleByIndexFunction? getTitle,
    int? tickCount,
    BorderSide? tickBorderData,
    BorderSide? gridBorderData,
    TextStyle? ticksTextStyle,
    Color? radarBackgroundColor,
    FlBorderData? borderData,
  }) : super(
          dataSets: dataSets,
          radarShape: radarShape,
          radarBorderData: radarBorderData,
          titlePositionPercentageOffset: titlePositionPercentageOffset,
          titleTextStyle: titleTextStyle,
          getTitle: getTitle,
          tickCount: tickCount,
          tickBorderData: tickBorderData,
          gridBorderData: gridBorderData,
          ticksTextStyle: ticksTextStyle,
          radarBackgroundColor: radarBackgroundColor,
          borderData: borderData,
        );

  @override
  RadarEntry get minEntry => super.maxEntry;
}
