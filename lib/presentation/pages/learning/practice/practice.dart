import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:overlay_tooltip/overlay_tooltip.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_constants.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';
import 'package:scrumpass_exam_simulator/app/extensions/color.dart';
import 'package:scrumpass_exam_simulator/app/widgets/log/system_log_event.dart';
import 'package:scrumpass_exam_simulator/data/models/enum/exam_enum.dart';
import 'package:scrumpass_exam_simulator/generated/assets.gen.dart';
import 'package:scrumpass_exam_simulator/generated/locales.g.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/home/<USER>';
import 'package:scrumpass_exam_simulator/presentation/controllers/learning/learning_controller.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/product_tour/product_tour_controller.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/learning/practice/practice_item_model.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/learning/practice/practice_tab.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/learning/widgets/coming_soon_tag.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/learning/widgets/fake_tab_tour.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/learning/widgets/premium_tag.dart';
import 'package:scrumpass_exam_simulator/presentation/widgets/product_tour_widget.dart';
import 'package:skeletonizer/skeletonizer.dart';

class Practice extends GetView<LearningController> {
  const Practice({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Stack(
            children: [
              ProductTourWidget(
                disable: controller.tourController.finishPrimaryTour.value,
                index: 1,
                title: LocaleKeys.learningMode.tr,
                icon: Container(
                  padding: EdgeInsets.only(right: 4),
                  child: Image.asset(
                    Assets.images.play.path,
                    height: 24,
                  ),
                ),
                percent: (100 / 6) * 1,
                description: LocaleKeys.getReadyLearning.tr,
                positionIndicator: TourController.positionCenter,
                tourBackAction: () async {
                  await Get.find<HomeController>().handleMoveTour(offset: 0);
                },
                child: DecoratedBox(
                  decoration: BoxDecoration(
                      color: AppColors.bgWhite,
                      borderRadius: BorderRadius.circular(8)),
                  child: Padding(
                    padding: const EdgeInsets.all(2),
                    child: TabBar(
                      controller: controller.tabController,
                      onTap: controller.onTabChanged,
                      isScrollable: false,
                      physics: const NeverScrollableScrollPhysics(),
                      indicator: BoxDecoration(
                          color: AppColors.primary,
                          borderRadius: BorderRadius.circular(8)),
                      padding: const EdgeInsets.all(0),
                      labelPadding: const EdgeInsets.all(0),
                      indicatorSize: TabBarIndicatorSize.tab,
                      indicatorPadding: const EdgeInsets.all(2),
                      labelColor: Colors.white,
                      labelStyle: AppTextStyles.smallMedium
                          .copyWith(color: Colors.white),
                      unselectedLabelStyle: AppTextStyles.smallMedium,
                      unselectedLabelColor: AppColors.primary,
                      dividerColor: Colors.transparent,
                      tabs: controller.tabs
                          .map(
                            (item) => SystemLogEvent(
                              eventName: item.eventName,
                              child: Tab(
                                text: item.title,
                                height: 36,
                              ),
                            ),
                          )
                          .toList(),
                    ),
                  ),
                ),
              ),
              fakeTabTour(controller, controller.mockTests.length)
            ],
          ),
          const SizedBox(height: 6),
          Flexible(
            child: Obx(() =>
                controller.selectedTab.value == PracticeTab.practice
                    ? GetBuilder<LearningController>(
                        id: LearningConstants.practiceTabContent,
                        builder: (_) => PracticesTab())
                    : GetBuilder<LearningController>(
                        id: LearningConstants.practiceTabContent,
                        builder: (_) => MocksTab())),
          ),
        ],
      );
    });
  }
}

class PracticesTab extends GetView<LearningController> {
  const PracticesTab({super.key});

  @override
  Widget build(BuildContext context) {
    final scrollController = ScrollController();

    final testInProgress = PracticeItemModel(
        title: LocaleKeys.testInProgress.tr,
        description: LocaleKeys.testInProgressDescription.tr,
        icon: Assets.images.restartSquare.path,
        iconBgColor: HexColor("FFF5E5"),
        onTap: () {
          controller.onTapTestInProgress();
        });

    final practiceItems = [
      PracticeItemModel(
          title: LocaleKeys.flashChallenge.tr,
          description: LocaleKeys.quickTestDescription.tr,
          icon: Assets.images.bolt.path,
          iconBgColor: HexColor("E5FFEB"),
          onTap: () {
            controller.onTapQuickTest();
          }),
      PracticeItemModel(
          title: LocaleKeys.masterYourMistakes.tr,
          description: LocaleKeys.masterYourMistakesDescription.tr,
          icon: Assets.images.notificationLinesRemove.path,
          iconBgColor: HexColor("FFE6DC"),
          isPremium: true,
          onTap: () {
            controller.onTapMissedQuestionsTest();
          }),
      PracticeItemModel(
          title: LocaleKeys.personalReview.tr,
          description: LocaleKeys.personalReviewDescription.tr,
          icon: Assets.images.bookmarkSquareMinimalistic.path,
          iconBgColor: HexColor("D9F1FF"),
          isPremium: true,
          onTap: () {
            controller.onTapBookmarkQuestionTest();
          }),
      PracticeItemModel(
          title: LocaleKeys.topicMastery.tr,
          description: LocaleKeys.topicMasteryDescription.tr,
          icon: Assets.images.palette.path,
          isPremium: true,
          iconBgColor: HexColor("EEECFC"),
          onTap: () {
            controller.onTapDomainSubjectTest();
          }),
      PracticeItemModel(
          title: LocaleKeys.flashcard.tr,
          description: LocaleKeys.flashcardDescription.tr,
          icon: Assets.images.sliderVertical.path,
          isPremium: true,
          isComingSoon: true,
          iconBgColor: HexColor("E6EFF2"),
          onTap: () {}),
    ];
    if (controller.testInProgress != null) {
      practiceItems.insert(0, testInProgress);
    }
    int indexTour = 0;
    return ListView.builder(
      controller: scrollController,
      shrinkWrap: true,
      physics: NeverScrollableScrollPhysics(),
      itemBuilder: (context, index) {
        if (controller.testInProgress != null && index == 0 ||
            practiceItems[index].title == LocaleKeys.flashcard.tr) {
          return PracticeItem(
            item: practiceItems[index],
          );
        }
        int itemIndex =
            (controller.testInProgress != null) ? (index + 2) : index + 3;
        indexTour += 1;
        return ProductTourWidget(
          title: practiceItems[index].title,
          description: controller.practiceTestsTour[indexTour - 1],
          icon: ProductTourWidget.generateImgIcon(itemIndex - 1),
          index: itemIndex,
          positionIndicator: TourController.positionLeft,
          position: (Global.isPremium &&
                      ([1, 2].contains(indexTour) ||
                          (indexTour == 3 &&
                              controller.relativeApps.isNotEmpty))) ||
                  index == 0 ||
                  ([1, 2].contains(index) && controller.relativeApps.isNotEmpty)
              ? TooltipVerticalPosition.BOTTOM
              : TooltipVerticalPosition.TOP,
          isPause: index == 3 ? true : false,
          percent: (100 / 6) * itemIndex,
          tourAction: () {
            if (index == 3) {
              controller.tourController.completeTourPrimary();
            }
          },
          child: PracticeItem(
            item: practiceItems[index],
          ),
        );
      },
      itemCount: practiceItems.length,
    );
  }
}

class MocksTab extends GetView<LearningController> {
  const MocksTab({super.key});

  @override
  Widget build(BuildContext context) {
    final testInProgress = PracticeItemModel(
        title: LocaleKeys.testInProgress.tr,
        description: LocaleKeys.testInProgressDescription.tr,
        icon: Assets.images.restartSquare.path,
        iconBgColor: HexColor("FFF5E5"),
        onTap: () {
          controller.onTapTestInProgress(fromMock: true);
        });
    final mockItems = controller.mockTests
        .map((e) => PracticeItemModel(
            title: e.quizName,
            description:
                "${e.noq} ${LocaleKeys.questions.tr}, ${e.duration} ${LocaleKeys.mins.tr}",
            icon: TestType.fullTest.icon,
            iconBgColor: TestType.fullTest.bgColor,
            isPremium: e.premium,
            onTap: () {
              controller.onTapMockTest(e);
            }))
        .toList();
    if (controller.testInProgress != null) {
      mockItems.insert(0, testInProgress);
    }
    final fakeMockItems = [
      PracticeItemModel(
          title: LocaleKeys.testInProgress.tr,
          description: LocaleKeys.testInProgressDescription.tr,
          icon: Assets.images.restartSquare.path,
          iconBgColor: HexColor("FFF5E5"),
          onTap: () {
            controller.onTapTestInProgress();
          }),
      PracticeItemModel(
          title: "Mini Test",
          description:
              "50 ${LocaleKeys.questions.tr}, 50 ${LocaleKeys.mins.tr}",
          icon: Assets.images.document.path,
          iconBgColor: HexColor("E4FDFB"),
          isPremium: true,
          onTap: () {}),
      PracticeItemModel(
          title: "Full Test",
          description:
              "100 ${LocaleKeys.questions.tr}, 100 ${LocaleKeys.mins.tr}",
          icon: Assets.images.documentsMinimalistic.path,
          iconBgColor: HexColor("EEECFC"),
          isPremium: true,
          onTap: () {}),
    ];
    bool firstTest = true;
    return Skeletonizer(
      enabled: controller.isGettingMockTest.value,
      child: ListView.builder(
        shrinkWrap: true,
        physics: NeverScrollableScrollPhysics(),
        itemBuilder: (context, index) {
          final item = controller.isGettingMockTest.value
              ? fakeMockItems[index]
              : mockItems[index];

          if (controller.testInProgress != null && index == 0) {
            return PracticeItem(item: item);
          }

          if (firstTest) {
            firstTest = false;
            return PracticeItem(item: item);
          }

          return PracticeItem(item: item);
        },
        itemCount: controller.isGettingMockTest.value
            ? fakeMockItems.length
            : mockItems.length,
      ),
    );
  }
}

class PracticeItem extends StatelessWidget {
  const PracticeItem({super.key, required this.item});

  final PracticeItemModel item;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: item.isComingSoon ? null : () => item.onTap.call(),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 6),
        child: DecoratedBox(
          decoration: BoxDecoration(
            color: const Color.fromRGBO(255, 255, 255, 1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Padding(
            padding: const EdgeInsets.all(8),
            child: IntrinsicHeight(
              child: Row(
                children: [
                  // Icon
                  DecoratedBox(
                    decoration: BoxDecoration(
                      color: item.iconBgColor,
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: SizedBox(
                      width: 48,
                      height: 48,
                      child: Center(
                        child: SvgPicture.asset(item.icon),
                      ),
                    ),
                  ),
                  const SizedBox(width: 10),

                  // Nội dung
                  Expanded(
                    child: Row(
                      children: [
                        Expanded(
                          child: SingleChildScrollView(
                            physics: NeverScrollableScrollPhysics(),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  item.title,
                                  style: AppTextStyles.baseMedium
                                      .copyWith(color: AppColors.text1st),
                                ),
                                if (item.description.isNotEmpty)
                                  Text(
                                    item.description,
                                    style: AppTextStyles.xSmallRegular
                                        .copyWith(color: AppColors.text2nd),
                                  ),
                              ],
                            ),
                          ),
                        ),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            if (item.isPremium)
                              const PremiumTagWidget()
                            else
                              const SizedBox(),
                            const Spacer(),
                            if (item.isComingSoon)
                              const ComingSoonTag()
                            else
                              Assets.images.arrowRight.svg(),
                          ],
                        ),
                      ],
                    ),
                  )
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
