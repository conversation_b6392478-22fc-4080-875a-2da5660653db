<?xml version="1.0" encoding="UTF-8"?>
<testsuites>
  <testsuite name="fastlane.lanes">
    
    
    
      
      <testcase classname="fastlane.lanes" name="0: default_platform" time="0.000123">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="1: Switch to android build_apk lane" time="5.5e-05">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="2: Switch to android sh_on_root lane" time="4.0e-05">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="3: cd /Users/<USER>/scrumpass-exam-simulator &amp;&amp; flutter build apk" time="39.905548">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="4: firebase_app_distribution" time="15.752386">
        
      </testcase>
    
  </testsuite>
</testsuites>
